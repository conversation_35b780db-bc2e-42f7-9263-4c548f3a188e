const fs = require('fs');
const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 JSON ↔ CSV 转换工具演示\n');

// 创建演示数据
const demoData = {
    "demo1": {
        "name": "演示产品 A",
        "price": 99.99,
        "inStock": true,
        "description": "这是一个包含中文的演示产品",
        "tags": "热门,推荐"
    },
    "demo2": {
        "name": "Demo Product B",
        "price": 149.50,
        "inStock": false,
        "description": "A product with \"quotes\" and, commas",
        "category": "electronics"
    },
    "demo3": {
        "name": "测试产品 C",
        "price": 0,
        "inStock": true,
        "description": "免费产品\n支持多行描述",
        "rating": 4.5
    }
};

// 步骤 1: 创建演示 JSON 文件
console.log('📝 步骤 1: 创建演示 JSON 文件...');
const demoJsonPath = './json/demo.json';
fs.writeFileSync(demoJsonPath, JSON.stringify(demoData, null, 2), 'utf8');
console.log('✅ 创建了 demo.json 文件\n');

// 步骤 2: 转换 JSON 到 CSV
console.log('🔄 步骤 2: 将 JSON 转换为 CSV...');
try {
    execSync('node jsonToCsv.js', { stdio: 'pipe' });
    console.log('✅ JSON 转 CSV 完成\n');
} catch (error) {
    console.error('❌ JSON 转 CSV 失败:', error.message);
    process.exit(1);
}

// 步骤 3: 显示生成的 CSV 内容
console.log('📄 步骤 3: 查看生成的 CSV 文件内容:');
const csvPath = './csv/demo.csv';
if (fs.existsSync(csvPath)) {
    const csvContent = fs.readFileSync(csvPath, 'utf8');
    console.log('--- demo.csv ---');
    console.log(csvContent);
    console.log('--- 结束 ---\n');
} else {
    console.log('❌ CSV 文件未找到\n');
}

// 步骤 4: 备份原始 JSON 并删除
console.log('💾 步骤 4: 备份原始 JSON 文件...');
const backupPath = './json/demo.json.original';
fs.copyFileSync(demoJsonPath, backupPath);
fs.unlinkSync(demoJsonPath);
console.log('✅ 原始文件已备份并删除\n');

// 步骤 5: 从 CSV 转换回 JSON
console.log('🔄 步骤 5: 将 CSV 转换回 JSON...');
try {
    execSync('node csvToJson.js', { stdio: 'pipe' });
    console.log('✅ CSV 转 JSON 完成\n');
} catch (error) {
    console.error('❌ CSV 转 JSON 失败:', error.message);
    process.exit(1);
}

// 步骤 6: 比较原始和转换后的 JSON
console.log('🔍 步骤 6: 验证转换一致性...');
if (fs.existsSync(demoJsonPath)) {
    const originalData = JSON.parse(fs.readFileSync(backupPath, 'utf8'));
    const convertedData = JSON.parse(fs.readFileSync(demoJsonPath, 'utf8'));
    
    // 简单比较
    const originalStr = JSON.stringify(originalData, Object.keys(originalData).sort());
    const convertedStr = JSON.stringify(convertedData, Object.keys(convertedData).sort());
    
    if (originalStr === convertedStr) {
        console.log('✅ 转换一致性验证通过！数据完全一致\n');
    } else {
        console.log('⚠️  转换后数据有细微差异，但这可能是正常的（如数据类型转换）\n');
        
        console.log('📊 详细比较:');
        console.log('原始数据键数量:', Object.keys(originalData).length);
        console.log('转换后数据键数量:', Object.keys(convertedData).length);
        
        // 显示转换后的内容
        console.log('\n--- 转换后的 JSON 内容 ---');
        console.log(JSON.stringify(convertedData, null, 2));
        console.log('--- 结束 ---\n');
    }
} else {
    console.log('❌ 转换后的 JSON 文件未找到\n');
}

// 步骤 7: 清理演示文件
console.log('🧹 步骤 7: 清理演示文件...');
if (fs.existsSync(backupPath)) {
    fs.unlinkSync(backupPath);
}
if (fs.existsSync(csvPath)) {
    fs.unlinkSync(csvPath);
}
if (fs.existsSync(demoJsonPath)) {
    fs.unlinkSync(demoJsonPath);
}
console.log('✅ 演示文件已清理\n');

console.log('🎉 演示完成！');
console.log('\n📚 使用说明:');
console.log('- 运行 `node jsonToCsv.js` 将 json/ 目录中的文件转换为 CSV');
console.log('- 运行 `node csvToJson.js` 将 csv/ 目录中的文件转换为 JSON');
console.log('- 运行 `node testConsistency.js` 测试转换一致性');
console.log('- 查看 README.md 了解详细使用方法');