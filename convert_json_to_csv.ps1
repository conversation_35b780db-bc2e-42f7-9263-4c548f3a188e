# 设置输入和输出目录
$jsonDir = ".\json"
$csvDir = ".\csv"

# 确保输出目录存在
if (-not (Test-Path $csvDir)) {
    New-Item -ItemType Directory -Path $csvDir -Force | Out-Null
}

# 获取所有JSON文件（不包括.meta文件）
$jsonFiles = Get-ChildItem -Path $jsonDir -Filter "*.json" | Where-Object { $_.Name -notlike "*.meta" }

# 处理每个JSON文件
foreach ($jsonFile in $jsonFiles) {
    Write-Host "Processing file: $($jsonFile.Name)"
    
    # 读取JSON内容
    $jsonContent = Get-Content -Path $jsonFile.FullName -Raw -Encoding UTF8
    
    try {
        # 解析JSON
        $jsonData = $jsonContent | ConvertFrom-Json
        
        # 创建CSV文件名
        $csvFileName = [System.IO.Path]::GetFileNameWithoutExtension($jsonFile.Name) + ".csv"
        $csvFilePath = Join-Path -Path $csvDir -ChildPath $csvFileName
        
        # 获取所有属性名称
        $properties = $jsonData | Get-Member -MemberType NoteProperty | Select-Object -ExpandProperty Name
        
        if ($properties -and $properties.Count -gt 0) {
            # 获取所有可能的列（合并所有对象的属性）
            $allColumns = @()
            foreach ($prop in $properties) {
                $item = $jsonData.$prop
                $itemProperties = $item | Get-Member -MemberType NoteProperty | Select-Object -ExpandProperty Name
                $allColumns += $itemProperties
            }
            $uniqueColumns = $allColumns | Select-Object -Unique
            
            # 创建CSV内容
            $csvRows = @()
            
            # 添加ID列和其他列
            $csvRows += "ID," + ($uniqueColumns -join ",")
            
            # 添加数据行
            foreach ($prop in $properties) {
                $item = $jsonData.$prop
                $row = "$prop,"
                
                foreach ($column in $uniqueColumns) {
                    if ($item | Get-Member -Name $column) {
                        $value = $item.$column
                        if ($null -ne $value) {
                            # 处理可能包含逗号的值
                            $valueStr = $value.ToString().Replace('"', '""')
                            $row += """$valueStr""," 
                        } else {
                            $row += ","
                        }
                    } else {
                        $row += ","
                    }
                }
                
                # 移除最后一个逗号
                $row = $row.TrimEnd(',')
                $csvRows += $row
            }
            
            # 写入CSV文件
            $csvRows | Out-File -FilePath $csvFilePath -Encoding UTF8
            
            Write-Host "Created CSV file: $csvFileName"
        } else {
            Write-Host "Warning: $($jsonFile.Name) does not contain valid JSON data or is empty"
        }
    } catch {
        Write-Host "Error: Failed to process $($jsonFile.Name): $_"
    }
}

Write-Host "All JSON files have been converted!"