# JSON ↔ CSV 转换工具

这个项目包含两个脚本，用于在 JSON 和 CSV 格式之间进行双向转换，确保数据格式的一致性。

## 文件说明

- `jsonToCsv.js` - 将 JSON 文件转换为 CSV 文件
- `csvToJson.js` - 将 CSV 文件转换回 JSON 文件
- `testConsistency.js` - 测试转换一致性的工具

## 目录结构

```
├── json/          # JSON 文件目录
├── csv/           # CSV 文件目录
├── jsonToCsv.js   # JSON 转 CSV 脚本
├── csvToJson.js   # CSV 转 JSON 脚本
└── testConsistency.js # 一致性测试脚本
```

## 使用方法

### JSON 转 CSV

```bash
node jsonToCsv.js
```

这个脚本会：
- 读取 `json/` 目录下的所有 `.json` 文件（排除 `.meta.json` 文件）
- 将每个 JSON 文件转换为对应的 CSV 文件
- 输出到 `csv/` 目录

### CSV 转 JSON

```bash
node csvToJson.js
```

这个脚本会：
- 读取 `csv/` 目录下的所有 `.csv` 文件
- 将每个 CSV 文件转换为对应的 JSON 文件
- 输出到 `json/` 目录

### 测试一致性

```bash
node testConsistency.js
```

这个脚本会验证 JSON → CSV → JSON 转换过程中数据的一致性。

## 数据格式说明

### JSON 格式

JSON 文件应该是一个对象，其中每个属性代表一个数据项：

```json
{
  "item1": {
    "name": "Product A",
    "price": 19.99,
    "description": "A great product",
    "tags": "new,popular"
  },
  "item2": {
    "name": "Product B",
    "price": 29.99,
    "description": "Another \"awesome\" product",
    "tags": "sale"
  }
}
```

### CSV 格式

对应的 CSV 文件格式：

```csv
ID,name,price,description,tags
item1,Product A,19.99,A great product,"new,popular"
item2,Product B,29.99,"Another ""awesome"" product",sale
```

## 特性

### 数据类型处理

- **数字**: 自动识别整数和浮点数
- **布尔值**: 支持 `true`/`false` 转换
- **字符串**: 正确处理包含逗号、引号和换行符的字符串
- **空值**: 空字段不会在 JSON 中创建对应属性

### CSV 特殊字符处理

- 包含逗号的值会被双引号包围
- 双引号会被转义为两个双引号
- 支持多行文本

### 错误处理

- 文件读取错误处理
- JSON 解析错误处理
- CSV 格式验证
- 详细的错误日志

## 注意事项

1. 确保 `json/` 和 `csv/` 目录存在（脚本会自动创建）
2. JSON 文件必须是有效的 JSON 格式
3. CSV 文件的第一行必须是标题行
4. CSV 文件的第一列被视为 ID 列
5. 转换过程会保持数据类型（数字、布尔值、字符串）

## 示例

### 完整转换流程

1. 准备 JSON 文件在 `json/` 目录
2. 运行 `node jsonToCsv.js` 生成 CSV 文件
3. 运行 `node csvToJson.js` 转换回 JSON
4. 运行 `node testConsistency.js` 验证一致性

### 批量处理

两个脚本都支持批量处理，会自动处理目录中的所有相关文件。

## 故障排除

- **文件不存在**: 检查文件路径和目录结构
- **JSON 解析错误**: 验证 JSON 文件格式
- **CSV 格式错误**: 检查 CSV 文件的列数和格式
- **权限错误**: 确保有读写目录的权限