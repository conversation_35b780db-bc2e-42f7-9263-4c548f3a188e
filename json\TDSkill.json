{"1000": {"id": 1000, "nextId": 1001, "class": 1, "type": 1, "name": "斩击", "desc": "朝前方斩击,并击退敌人", "lv": 1, "dam": 1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.5, "barrageNum": 1, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 1, "releaseCd": 0.3, "scale": 1.4, "isPen": 999, "isRepel": 1, "repelDic": 150, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_zj", "skillClass": "Skill_NormalChop", "skillCompose": 8, "show": 1}, "1001": {"id": 1001, "nextId": 1002, "class": 1, "type": 1, "name": "斩击", "desc": "斩击次数+1", "lv": 2, "dam": 1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.5, "barrageNum": 2, "barrageCd": 0.1, "barrageAngle": 30, "barrangeSpeed": 0, "releaseCount": 1, "releaseCd": 0.3, "scale": 1.4, "isPen": 999, "isRepel": 1, "repelDic": 150, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_zj", "skillClass": "Skill_NormalChop", "skillCompose": 8, "show": 1}, "1002": {"id": 1002, "nextId": 1003, "class": 1, "type": 1, "name": "斩击", "desc": "伤害+50%，次数+1", "lv": 3, "dam": 1.5, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.5, "barrageNum": 3, "barrageCd": 0.1, "barrageAngle": 35, "barrangeSpeed": 0, "releaseCount": 1, "releaseCd": 0.3, "scale": 1.4, "isPen": 999, "isRepel": 1, "repelDic": 150, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_zj", "skillClass": "Skill_NormalChop", "skillCompose": 5, "show": 1, "isAdGet": 1}, "1003": {"id": 1003, "nextId": 1004, "class": 1, "type": 1, "name": "斩击", "desc": "伤害+20%", "lv": 4, "dam": 1.7, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.5, "barrageNum": 3, "barrageCd": 0.1, "barrageAngle": 35, "barrangeSpeed": 0, "releaseCount": 1, "releaseCd": 0.3, "scale": 1.4, "isPen": 999, "isRepel": 1, "repelDic": 150, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_zj", "skillClass": "Skill_NormalChop", "skillCompose": 8, "show": 1}, "1004": {"id": 1004, "nextId": 1005, "class": 1, "type": 1, "name": "斩击", "desc": "伤害+20%", "lv": 5, "dam": 1.9, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.5, "barrageNum": 3, "barrageCd": 0.1, "barrageAngle": 35, "barrangeSpeed": 0, "releaseCount": 1, "releaseCd": 0.3, "scale": 1.4, "isPen": 999, "isRepel": 1, "repelDic": 150, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_zj", "skillClass": "Skill_NormalChop", "skillCompose": 8, "show": 1}, "1005": {"id": 1005, "class": 1, "type": 1, "name": "斩击", "desc": "伤害+150%，朝4周斩击", "lv": 6, "dam": 2.9, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.5, "barrageNum": 6, "barrageCd": 0, "barrageAngle": 60, "barrangeSpeed": 0, "releaseCount": 1, "releaseCd": 0.3, "scale": 1.4, "isPen": 999, "isRepel": 1, "repelDic": 150, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_zj", "skillClass": "Skill_NormalChop", "skillCompose": 5, "show": 1, "isAdGet": 1}, "1020": {"id": 1020, "nextId": 1021, "class": 1, "type": 1, "name": "魔法球", "desc": "发射魔法球", "lv": 1, "dam": 1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.8, "barrageNum": 1, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 700, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_mfq", "skillClass": "Skill_MagicBall", "skillCompose": 8, "show": 1, "hitEffect": "Effect_1020"}, "1021": {"id": 1021, "nextId": 1022, "class": 1, "type": 1, "name": "魔法球", "desc": "伤害+10%", "lv": 2, "dam": 1.1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.8, "barrageNum": 2, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 700, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_mfq", "skillClass": "Skill_MagicBall", "skillCompose": 8, "show": 1, "hitEffect": "Effect_1020"}, "1022": {"id": 1022, "nextId": 1023, "class": 1, "type": 1, "name": "魔法球", "desc": "伤害+50%", "lv": 3, "dam": 1.6, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.8, "barrageNum": 4, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 700, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_mfq", "skillClass": "Skill_MagicBall", "skillCompose": 5, "show": 1, "isAdGet": 1, "hitEffect": "Effect_1020"}, "1023": {"id": 1023, "nextId": 1024, "class": 1, "type": 1, "name": "魔法球", "desc": "伤害+10%", "lv": 4, "dam": 1.7, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.8, "barrageNum": 5, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 700, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_mfq", "skillClass": "Skill_MagicBall", "skillCompose": 8, "show": 1, "hitEffect": "Effect_1020"}, "1024": {"id": 1024, "nextId": 1025, "class": 1, "type": 1, "name": "魔法球", "desc": "伤害+10%", "lv": 5, "dam": 1.8, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.8, "barrageNum": 5, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 700, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_mfq", "skillClass": "Skill_MagicBall", "skillCompose": 8, "show": 1, "hitEffect": "Effect_1020"}, "1025": {"id": 1025, "class": 1, "type": 1, "name": "魔法球", "desc": "伤害+100%，数量+1", "lv": 6, "dam": 2.8, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.8, "barrageNum": 8, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 700, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_mfq", "skillClass": "Skill_MagicBall", "skillCompose": 5, "show": 1, "isAdGet": 1, "hitEffect": "Effect_1020"}, "1040": {"id": 1040, "nextId": 1041, "class": 1, "type": 1, "name": "闪电链", "desc": "召唤闪电链，链接敌人", "lv": 1, "dam": 1.1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.5, "barrageNum": 2, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 0.7, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_sdl", "skillClass": "Skill_<PERSON><PERSON><PERSON><PERSON>", "skillCompose": 8, "show": 1}, "1041": {"id": 1041, "nextId": 1042, "class": 1, "type": 1, "name": "闪电链", "desc": "伤害+20%，数量+1", "lv": 2, "dam": 1.3, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.5, "barrageNum": 3, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 0.7, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_sdl", "skillClass": "Skill_<PERSON><PERSON><PERSON><PERSON>", "skillCompose": 8, "show": 1}, "1042": {"id": 1042, "nextId": 1043, "class": 1, "type": 1, "name": "闪电链", "desc": "伤害+50%，数量+3", "lv": 3, "dam": 1.8, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.5, "barrageNum": 4, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 0.7, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_sdl", "skillClass": "Skill_<PERSON><PERSON><PERSON><PERSON>", "skillCompose": 5, "show": 1, "isAdGet": 1}, "1043": {"id": 1043, "nextId": 1044, "class": 1, "type": 1, "name": "闪电链", "desc": "伤害+20%，数量+1", "lv": 4, "dam": 1.9, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.5, "barrageNum": 5, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 0.7, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_sdl", "skillClass": "Skill_<PERSON><PERSON><PERSON><PERSON>", "skillCompose": 8, "show": 1}, "1044": {"id": 1044, "nextId": 1045, "class": 1, "type": 1, "name": "闪电链", "desc": "伤害+30%", "lv": 5, "dam": 2.2, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.5, "barrageNum": 6, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 0.7, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_sdl", "skillClass": "Skill_<PERSON><PERSON><PERSON><PERSON>", "skillCompose": 8, "show": 1}, "1045": {"id": 1045, "class": 1, "type": 1, "name": "闪电链", "desc": "伤害+80%，数量+6，cd减少20%", "lv": 6, "dam": 3, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.4, "barrageNum": 7, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 0.7, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_sdl", "skillClass": "Skill_<PERSON><PERSON><PERSON><PERSON>", "skillCompose": 5, "show": 1, "isAdGet": 1}, "1060": {"id": 1060, "nextId": 1061, "class": 1, "type": 1, "name": "雷击", "desc": "召唤闪电，对敌人造成伤害", "lv": 1, "dam": 1.2, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 1, "barrageCd": 0.3, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 0.8, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 2, "icon": "img/skill/icon/skill_lj", "skillClass": "Skill_StruckLightning", "skillCompose": 8, "show": 1}, "1061": {"id": 1061, "nextId": 1062, "class": 1, "type": 1, "name": "雷击", "desc": "数量+1", "lv": 2, "dam": 1.2, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 2, "barrageCd": 0.3, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 0.8, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 2, "icon": "img/skill/icon/skill_lj", "skillClass": "Skill_StruckLightning", "skillCompose": 8, "show": 1}, "1062": {"id": 1062, "nextId": 1063, "class": 1, "type": 1, "name": "雷击", "desc": "伤害+50%，数量+2", "lv": 3, "dam": 1.7, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 4, "barrageCd": 0.3, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 0.8, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 2, "icon": "img/skill/icon/skill_lj", "skillClass": "Skill_StruckLightning", "skillCompose": 5, "show": 1, "isAdGet": 1}, "1063": {"id": 1063, "nextId": 1064, "class": 1, "type": 1, "name": "雷击", "desc": "伤害+30%", "lv": 4, "dam": 2, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 4, "barrageCd": 0.3, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 0.8, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 2, "icon": "img/skill/icon/skill_lj", "skillClass": "Skill_StruckLightning", "skillCompose": 8, "show": 1}, "1064": {"id": 1064, "nextId": 1065, "class": 1, "type": 1, "name": "雷击", "desc": "伤害+20%，数量+1", "lv": 5, "dam": 2.2, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 4, "barrageCd": 0.3, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 0.8, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 2, "icon": "img/skill/icon/skill_lj", "skillClass": "Skill_StruckLightning", "skillCompose": 8, "show": 1}, "1065": {"id": 1065, "class": 1, "type": 1, "name": "雷击", "desc": "伤害+100%,范围+50%，数量+3", "lv": 6, "dam": 3.2, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 4, "barrageCd": 0.3, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 2, "icon": "img/skill/icon/skill_lj", "skillClass": "Skill_StruckLightning", "skillCompose": 5, "show": 1, "isAdGet": 1}, "1080": {"id": 1080, "nextId": 1081, "class": 1, "type": 1, "name": "陨石", "desc": "从空中随机砸落陨石，击退敌人", "lv": 1, "dam": 1.6, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 1, "barrageCd": 0.5, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 1, "repelDic": 100, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_ys", "skillClass": "Skill_Meteorite", "skillCompose": 8, "show": 1}, "1081": {"id": 1081, "nextId": 1082, "class": 1, "type": 1, "name": "陨石", "desc": "数量+1", "lv": 2, "dam": 1.6, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 2, "barrageCd": 0.5, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 1, "repelDic": 100, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_ys", "skillClass": "Skill_Meteorite", "skillCompose": 8, "show": 1}, "1082": {"id": 1082, "nextId": 1083, "class": 1, "type": 1, "name": "陨石", "desc": "伤害+50%，数量+3", "lv": 3, "dam": 2.1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 5, "barrageCd": 0.5, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 1, "repelDic": 100, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_ys", "skillClass": "Skill_Meteorite", "skillCompose": 5, "show": 1, "isAdGet": 1}, "1083": {"id": 1083, "nextId": 1084, "class": 1, "type": 1, "name": "陨石", "desc": "伤害+10%，范围+10%", "lv": 4, "dam": 2.2, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 5, "barrageCd": 0.5, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1.1, "isPen": 999, "isRepel": 1, "repelDic": 100, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_ys", "skillClass": "Skill_Meteorite", "skillCompose": 8, "show": 1}, "1084": {"id": 1084, "nextId": 1085, "class": 1, "type": 1, "name": "陨石", "desc": "伤害+30%", "lv": 5, "dam": 2.5, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 5, "barrageCd": 0.5, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1.1, "isPen": 999, "isRepel": 1, "repelDic": 100, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_ys", "skillClass": "Skill_Meteorite", "skillCompose": 8, "show": 1}, "1085": {"id": 1085, "nextId": 1086, "class": 1, "type": 1, "name": "陨石", "desc": "伤害+100%，范围+100%", "lv": 6, "dam": 3.5, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 5, "barrageCd": 0.5, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 2.2, "isPen": 999, "isRepel": 1, "repelDic": 100, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_ys", "skillClass": "Skill_Meteorite", "skillCompose": 5, "show": 1, "isAdGet": 1}, "1100": {"id": 1100, "nextId": 1101, "class": 1, "type": 1, "name": "冰爆", "desc": "在脚下产生范围爆炸", "lv": 1, "dam": 2.5, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 5, "barrageNum": 1, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 1, "repelDic": 30, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_bb", "skillClass": "Skill_RangeBomb", "skillCompose": 8, "show": 1}, "1101": {"id": 1101, "nextId": 1102, "class": 1, "type": 1, "name": "冰爆", "desc": "范围+10%", "lv": 2, "dam": 2.5, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 5, "barrageNum": 1, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 1, "repelDic": 30, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_bb", "skillClass": "Skill_RangeBomb", "skillCompose": 8, "show": 1}, "1102": {"id": 1102, "nextId": 1103, "class": 1, "type": 1, "name": "冰爆", "desc": "伤害+80%，并减速敌人", "lv": 3, "dam": 3.3, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 5, "barrageNum": 1, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 1, "repelDic": 30, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_bb", "skillClass": "Skill_RangeBomb", "skillCompose": 5, "show": 1, "isAdGet": 1, "buffId": "2,1001"}, "1103": {"id": 1103, "nextId": 1104, "class": 1, "type": 1, "name": "冰爆", "desc": "伤害+10%，cd减少10%", "lv": 4, "dam": 3.4, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 4, "barrageNum": 1, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 1, "repelDic": 30, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_bb", "skillClass": "Skill_RangeBomb", "skillCompose": 8, "show": 1, "buffId": "2,1001"}, "1104": {"id": 1104, "nextId": 1105, "class": 1, "type": 1, "name": "冰爆", "desc": "cd-20%", "lv": 5, "dam": 3.4, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 3, "barrageNum": 1, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 1, "repelDic": 30, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_bb", "skillClass": "Skill_RangeBomb", "skillCompose": 8, "show": 1, "buffId": "2,1001"}, "1105": {"id": 1105, "class": 1, "type": 1, "name": "冰爆", "desc": "伤害+100%，范围+50%", "lv": 6, "dam": 4.4, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 3, "barrageNum": 1, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1.1, "isPen": 999, "isRepel": 1, "repelDic": 30, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_bb", "skillClass": "Skill_RangeBomb", "skillCompose": 5, "show": 1, "isAdGet": 1, "buffId": "2,1001"}, "1120": {"id": 1120, "nextId": 1121, "class": 1, "type": 1, "name": "旋转火球", "desc": "召唤火球围绕自身旋转", "lv": 1, "dam": 1.2, "area": -1, "dis": 0, "dur": 5, "damCd": 0, "cd": 2, "barrageNum": 2, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 100, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_xzhq", "skillClass": "Skill_RingFireballs", "skillCompose": 8, "show": 1}, "1121": {"id": 1121, "nextId": 1122, "class": 1, "type": 1, "name": "旋转火球", "desc": "伤害+10%", "lv": 2, "dam": 1.3, "area": -1, "dis": 0, "dur": 6, "damCd": 0, "cd": 2, "barrageNum": 2, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 150, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_xzhq", "skillClass": "Skill_RingFireballs", "skillCompose": 8, "show": 1}, "1122": {"id": 1122, "nextId": 1123, "class": 1, "type": 1, "name": "旋转火球", "desc": "伤害+80%，数量+2", "lv": 3, "dam": 2.1, "area": -1, "dis": 0, "dur": 7, "damCd": 0, "cd": 2, "barrageNum": 4, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 150, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_xzhq", "skillClass": "Skill_RingFireballs", "skillCompose": 5, "show": 1, "isAdGet": 1}, "1123": {"id": 1123, "nextId": 1124, "class": 1, "type": 1, "name": "旋转火球", "desc": "伤害+20%", "lv": 4, "dam": 2.3, "area": -1, "dis": 0, "dur": 8, "damCd": 0, "cd": 2, "barrageNum": 4, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 150, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_xzhq", "skillClass": "Skill_RingFireballs", "skillCompose": 8, "show": 1}, "1124": {"id": 1124, "nextId": 1125, "class": 1, "type": 1, "name": "旋转火球", "desc": "伤害+20%，数量+1", "lv": 5, "dam": 2.5, "area": -1, "dis": 0, "dur": 9, "damCd": 0, "cd": 2, "barrageNum": 5, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 150, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_xzhq", "skillClass": "Skill_RingFireballs", "skillCompose": 8, "show": 1}, "1125": {"id": 1125, "class": 1, "type": 1, "name": "旋转火球", "desc": "伤害+120%，火球体积+20%", "lv": 6, "dam": 3.5, "area": -1, "dis": 0, "dur": 10, "damCd": 0, "cd": 2, "barrageNum": 5, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 150, "releaseCount": 0, "releaseCd": 0, "scale": 1.2, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_xzhq", "skillClass": "Skill_RingFireballs", "skillCompose": 5, "show": 1, "isAdGet": 1}, "1140": {"id": 1140, "nextId": 1141, "class": 1, "type": 1, "name": "多重箭", "desc": "朝前方射出多支箭矢", "lv": 1, "dam": 0.9, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 3, "barrageCd": 0, "barrageAngle": 20, "barrangeSpeed": 500, "releaseCount": 1, "releaseCd": 0.2, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 1, "icon": "img/skill/icon/skill_dcj", "skillClass": "Skill_Multishot", "skillCompose": 8, "show": 1}, "1141": {"id": 1141, "nextId": 1142, "class": 1, "type": 1, "name": "多重箭", "desc": "箭矢数量+1", "lv": 2, "dam": 0.9, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 4, "barrageCd": 0, "barrageAngle": 20, "barrangeSpeed": 500, "releaseCount": 1, "releaseCd": 0.2, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 1, "icon": "img/skill/icon/skill_dcj", "skillClass": "Skill_Multishot", "skillCompose": 8, "show": 1}, "1142": {"id": 1142, "nextId": 1143, "class": 1, "type": 1, "name": "多重箭", "desc": "伤害+100%，箭矢数量+1", "lv": 3, "dam": 1.9, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 5, "barrageCd": 0, "barrageAngle": 20, "barrangeSpeed": 500, "releaseCount": 1, "releaseCd": 0.2, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 1, "icon": "img/skill/icon/skill_dcj", "skillClass": "Skill_Multishot", "skillCompose": 5, "show": 1, "isAdGet": 1}, "1143": {"id": 1143, "nextId": 1144, "class": 1, "type": 1, "name": "多重箭", "desc": "伤害+20%，箭矢数量+1", "lv": 4, "dam": 2.1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 6, "barrageCd": 0, "barrageAngle": 20, "barrangeSpeed": 500, "releaseCount": 1, "releaseCd": 0.2, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 1, "icon": "img/skill/icon/skill_dcj", "skillClass": "Skill_Multishot", "skillCompose": 8, "show": 1}, "1144": {"id": 1144, "nextId": 1145, "class": 1, "type": 1, "name": "多重箭", "desc": "伤害+20%，箭矢数量+1", "lv": 5, "dam": 2.3, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 7, "barrageCd": 0, "barrageAngle": 20, "barrangeSpeed": 500, "releaseCount": 1, "releaseCd": 0.2, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 1, "icon": "img/skill/icon/skill_dcj", "skillClass": "Skill_Multishot", "skillCompose": 8, "show": 1}, "1145": {"id": 1145, "class": 1, "type": 1, "name": "多重箭", "desc": "伤害+120%，箭矢数量+1", "lv": 6, "dam": 3.5, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 8, "barrageCd": 0, "barrageAngle": 20, "barrangeSpeed": 500, "releaseCount": 1, "releaseCd": 0.2, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 1, "icon": "img/skill/icon/skill_dcj", "skillClass": "Skill_Multishot", "skillCompose": 5, "show": 1, "isAdGet": 1}, "1160": {"id": 1160, "nextId": 1161, "class": 1, "type": 1, "name": "光之守卫", "desc": "在四周召唤守卫", "lv": 1, "dam": 5, "area": -1, "dis": 0, "dur": 0.6, "damCd": 0.3, "cd": 0.5, "barrageNum": 1, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "skillClass": "Skill_LaserRadiationGuard", "skillCompose": 8, "show": 1}, "1161": {"id": 1161, "nextId": 1162, "class": 1, "type": 1, "name": "光之守卫", "desc": "数量+1", "lv": 2, "dam": 5, "area": -1, "dis": 0, "dur": 0.6, "damCd": 0.3, "cd": 0.5, "barrageNum": 2, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1.1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "skillClass": "Skill_LaserRadiationGuard", "skillCompose": 8, "show": 1}, "1162": {"id": 1162, "nextId": 1163, "class": 1, "type": 1, "name": "光之守卫", "desc": "伤害+50%，数量+3", "lv": 3, "dam": 5, "area": -1, "dis": 0, "dur": 0.6, "damCd": 0.3, "cd": 0.5, "barrageNum": 5, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1.1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "skillClass": "Skill_LaserRadiationGuard", "skillCompose": 5, "show": 1, "isAdGet": 1}, "1163": {"id": 1163, "nextId": 1164, "class": 1, "type": 1, "name": "光之守卫", "desc": "持续时间+0.3s", "lv": 4, "dam": 5, "area": -1, "dis": 0, "dur": 0.9, "damCd": 0.3, "cd": 0.5, "barrageNum": 5, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1.1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "skillClass": "Skill_LaserRadiationGuard", "skillCompose": 8, "show": 1}, "1164": {"id": 1164, "nextId": 1165, "class": 1, "type": 1, "name": "光之守卫", "desc": "伤害+10%，持续时间+0.3s", "lv": 5, "dam": 5, "area": -1, "dis": 0, "dur": 1.2, "damCd": 0.3, "cd": 0.5, "barrageNum": 5, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1.1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "skillClass": "Skill_LaserRadiationGuard", "skillCompose": 8, "show": 1}, "1165": {"id": 1165, "class": 1, "type": 1, "name": "光之守卫", "desc": "伤害+150%，伤害间隔减10%", "lv": 6, "dam": 5, "area": -1, "dis": 0, "dur": 1.2, "damCd": 0.2, "cd": 0.5, "barrageNum": 5, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1.4, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "skillClass": "Skill_LaserRadiationGuard", "skillCompose": 5, "show": 1, "isAdGet": 1}, "1180": {"id": 1180, "nextId": 1181, "class": 1, "type": 1, "name": "烈焰炙烤", "desc": "发射火焰光束造成持续伤害", "lv": 1, "dam": 1, "area": -1, "dis": 0, "dur": 10, "damCd": 0.5, "cd": 1.5, "barrageNum": 1, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 2, "icon": "img/skill/icon/skill_lyzk", "skillClass": "Skill_LaserRadiation", "skillCompose": 8, "show": 1}, "1181": {"id": 1181, "nextId": 1182, "class": 1, "type": 1, "name": "烈焰炙烤", "desc": "持续时间+2s", "lv": 2, "dam": 1, "area": -1, "dis": 0, "dur": 3, "damCd": 0.5, "cd": 1.5, "barrageNum": 1, "barrageCd": 0.2, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 2, "icon": "img/skill/icon/skill_lyzk", "skillClass": "Skill_LaserRadiation", "skillCompose": 8, "show": 1}, "1182": {"id": 1182, "nextId": 1183, "class": 1, "type": 1, "name": "烈焰炙烤", "desc": "伤害+80%，光线体积+50%", "lv": 3, "dam": 1.8, "area": -1, "dis": 0, "dur": 5, "damCd": 0.5, "cd": 1.5, "barrageNum": 1, "barrageCd": 0.2, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1.1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 2, "icon": "img/skill/icon/skill_lyzk", "skillClass": "Skill_LaserRadiation", "skillCompose": 5, "show": 1, "isAdGet": 1}, "1183": {"id": 1183, "nextId": 1184, "class": 1, "type": 1, "name": "烈焰炙烤", "desc": "伤害+20%", "lv": 4, "dam": 1.8, "area": -1, "dis": 0, "dur": 5, "damCd": 0.5, "cd": 1.5, "barrageNum": 1, "barrageCd": 0.2, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1.1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 2, "icon": "img/skill/icon/skill_lyzk", "skillClass": "Skill_LaserRadiation", "skillCompose": 8, "show": 1}, "1184": {"id": 1184, "nextId": 1185, "class": 1, "type": 1, "name": "烈焰炙烤", "desc": "伤害+10%，cd减少10%", "lv": 5, "dam": 1.8, "area": -1, "dis": 0, "dur": 5, "damCd": 0.5, "cd": 1.4, "barrageNum": 1, "barrageCd": 0.2, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1.1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 2, "icon": "img/skill/icon/skill_lyzk", "skillClass": "Skill_LaserRadiation", "skillCompose": 8, "show": 1}, "1185": {"id": 1185, "class": 1, "type": 1, "name": "烈焰炙烤", "desc": "伤害+100%，光线体积+50%，", "lv": 6, "dam": 2.8, "area": -1, "dis": 0, "dur": 5, "damCd": 0.5, "cd": 1.4, "barrageNum": 1, "barrageCd": 0.2, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1.2, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 2, "icon": "img/skill/icon/skill_lyzk", "skillClass": "Skill_LaserRadiation", "skillCompose": 5, "show": 1, "isAdGet": 1}, "1200": {"id": 1200, "nextId": 1201, "class": 1, "type": 1, "name": "寒冰锥", "desc": "朝随机方向发射，命中敌人后会减速", "lv": 1, "dam": 1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 2, "barrageCd": 0, "barrageAngle": 30, "barrangeSpeed": 800, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_hbz", "skillClass": "Skill_Icicle", "skillCompose": 8, "show": 1, "buffId": "2,1001"}, "1201": {"id": 1201, "nextId": 1202, "class": 1, "type": 1, "name": "寒冰锥", "desc": "伤害+20%", "lv": 2, "dam": 1.2, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 2, "barrageCd": 0, "barrageAngle": 30, "barrangeSpeed": 800, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_hbz", "skillClass": "Skill_Icicle", "skillCompose": 8, "show": 1, "buffId": "2,1001"}, "1202": {"id": 1202, "nextId": 1203, "class": 1, "type": 1, "name": "寒冰锥", "desc": "伤害+80%，数量+2", "lv": 3, "dam": 2, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 4, "barrageCd": 0, "barrageAngle": 30, "barrangeSpeed": 800, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_hbz", "skillClass": "Skill_Icicle", "skillCompose": 5, "show": 1, "isAdGet": 1, "buffId": "2,1001"}, "1203": {"id": 1203, "nextId": 1204, "class": 1, "type": 1, "name": "寒冰锥", "desc": "数量+1", "lv": 4, "dam": 2, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 5, "barrageCd": 0, "barrageAngle": 30, "barrangeSpeed": 800, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_hbz", "skillClass": "Skill_Icicle", "skillCompose": 8, "show": 1, "buffId": "2,1001"}, "1204": {"id": 1204, "nextId": 1205, "class": 1, "type": 1, "name": "寒冰锥", "desc": "伤害+30%", "lv": 5, "dam": 2.3, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 5, "barrageCd": 0, "barrageAngle": 30, "barrangeSpeed": 800, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_hbz", "skillClass": "Skill_Icicle", "skillCompose": 8, "show": 1, "buffId": "2,1001"}, "1205": {"id": 1205, "class": 1, "type": 1, "name": "寒冰锥", "desc": "伤害+100%，数量+3", "lv": 6, "dam": 3.3, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 8, "barrageCd": 0, "barrageAngle": 30, "barrangeSpeed": 800, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_hbz", "skillClass": "Skill_Icicle", "skillCompose": 5, "show": 1, "isAdGet": 1, "buffId": "2,1001"}, "1220": {"id": 1220, "nextId": 1221, "class": 1, "type": 1, "name": "法阵", "desc": "创造一个法阵，抵御怪物", "lv": 1, "dam": 0.7, "area": 300, "dis": 0, "dur": -1, "damCd": 0.6, "cd": 0, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 3, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_fz", "skillClass": "Skill_Venom", "skillCompose": 8, "show": 1}, "1221": {"id": 1221, "nextId": 1222, "class": 1, "type": 1, "name": "法阵", "desc": "范围+10%", "lv": 2, "dam": 0.7, "area": 330, "dis": 0, "dur": -1, "damCd": 0.6, "cd": 0, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 3.3, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_fz", "skillClass": "Skill_Venom", "skillCompose": 8, "show": 1}, "1222": {"id": 1222, "nextId": 1223, "class": 1, "type": 1, "name": "法阵", "desc": "范围+30%,伤害+50%", "lv": 3, "dam": 1.2, "area": 390, "dis": 0, "dur": -1, "damCd": 0.6, "cd": 0, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 4.8, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_fz", "skillClass": "Skill_Venom", "skillCompose": 5, "show": 1, "isAdGet": 1}, "1223": {"id": 1223, "nextId": 1224, "class": 1, "type": 1, "name": "法阵", "desc": "伤害+10%，范围+10%", "lv": 4, "dam": 1.3, "area": 450, "dis": 0, "dur": -1, "damCd": 0.6, "cd": 0, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 5.1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_fz", "skillClass": "Skill_Venom", "skillCompose": 8, "show": 1}, "1224": {"id": 1224, "nextId": 1225, "class": 1, "type": 1, "name": "法阵", "desc": "伤害+10%，范围+10%", "lv": 5, "dam": 1.4, "area": 480, "dis": 0, "dur": -1, "damCd": 0.6, "cd": 0, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 5.4, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_fz", "skillClass": "Skill_Venom", "skillCompose": 8, "show": 1}, "1225": {"id": 1225, "class": 1, "type": 1, "name": "法阵", "desc": "范围+100%,伤害+100%", "lv": 6, "dam": 2.4, "area": 780, "dis": 0, "dur": -1, "damCd": 0.48, "cd": 0, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 5.4, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_fz", "skillClass": "Skill_Venom", "skillCompose": 5, "show": 1, "isAdGet": 1}, "1240": {"id": 1240, "nextId": 1241, "class": 1, "type": 1, "name": "箭雨", "desc": "箭雨", "lv": 1, "dam": 0.7, "area": 150, "dis": 0, "dur": 3, "damCd": 0, "cd": 1.5, "barrageNum": 1, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 2000, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 0, "icon": "img/skill/icon/skill_jy", "skillClass": "Skill_Arrows", "skillCompose": 8, "show": 1}, "1241": {"id": 1241, "nextId": 1242, "class": 1, "type": 1, "name": "箭雨", "desc": "持续时间+1s", "lv": 2, "dam": 0.7, "area": 150, "dis": 0, "dur": 4, "damCd": 0, "cd": 1.5, "barrageNum": 1, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 2000, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 0, "icon": "img/skill/icon/skill_jy", "skillClass": "Skill_Arrows", "skillCompose": 8, "show": 1}, "1242": {"id": 1242, "nextId": 1243, "class": 1, "type": 1, "name": "箭雨", "desc": "伤害+80%,范围+20%", "lv": 3, "dam": 1.5, "area": 150, "dis": 0, "dur": 4, "damCd": 0, "cd": 1.5, "barrageNum": 1, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 2000, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 0, "icon": "img/skill/icon/skill_jy", "skillClass": "Skill_Arrows", "skillCompose": 5, "show": 1, "isAdGet": 1}, "1243": {"id": 1243, "nextId": 1244, "class": 1, "type": 1, "name": "箭雨", "desc": "伤害+10%", "lv": 4, "dam": 1.6, "area": 150, "dis": 0, "dur": 4, "damCd": 0, "cd": 1.5, "barrageNum": 1, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 2000, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 0, "icon": "img/skill/icon/skill_jy", "skillClass": "Skill_Arrows", "skillCompose": 8, "show": 1}, "1244": {"id": 1244, "nextId": 1245, "class": 1, "type": 1, "name": "箭雨", "desc": "伤害+10%", "lv": 5, "dam": 1.7, "area": 150, "dis": 0, "dur": 4, "damCd": 0, "cd": 1.5, "barrageNum": 1, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 2000, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 0, "icon": "img/skill/icon/skill_jy", "skillClass": "Skill_Arrows", "skillCompose": 8, "show": 1}, "1245": {"id": 1245, "class": 1, "type": 1, "name": "箭雨", "desc": "伤害+100%，持续时间+3s", "lv": 6, "dam": 2.7, "area": 150, "dis": 0, "dur": 7, "damCd": 0, "cd": 1.5, "barrageNum": 1, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 2000, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 0, "icon": "img/skill/icon/skill_jy", "skillClass": "Skill_Arrows", "skillCompose": 5, "show": 1, "isAdGet": 1}, "1260": {"id": 1260, "nextId": 1261, "class": 1, "type": 1, "name": "螺旋镖", "desc": "发射飞镖在怪物之间来回弹射", "lv": 1, "dam": 1.25, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.5, "barrageNum": 1, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 800, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_lxb", "skillClass": "Skill_BounceThrowingKnife", "skillCompose": 8, "show": 1, "param": 2}, "1261": {"id": 1261, "nextId": 1262, "class": 1, "type": 1, "name": "螺旋镖", "desc": "数量+1", "lv": 2, "dam": 1.25, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.5, "barrageNum": 2, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 800, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_lxb", "skillClass": "Skill_BounceThrowingKnife", "skillCompose": 8, "show": 1, "param": 2}, "1262": {"id": 1262, "nextId": 1263, "class": 1, "type": 1, "name": "螺旋镖", "desc": "伤害+80%,数量+3", "lv": 3, "dam": 2.05, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.5, "barrageNum": 5, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 800, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_lxb", "skillClass": "Skill_BounceThrowingKnife", "skillCompose": 5, "show": 1, "isAdGet": 1, "param": 2}, "1263": {"id": 1263, "nextId": 1264, "class": 1, "type": 1, "name": "螺旋镖", "desc": "伤害+20%", "lv": 4, "dam": 2.25, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.5, "barrageNum": 5, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 800, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_lxb", "skillClass": "Skill_BounceThrowingKnife", "skillCompose": 8, "show": 1, "param": 2}, "1264": {"id": 1264, "nextId": 1265, "class": 1, "type": 1, "name": "螺旋镖", "desc": "伤害+10%，数量+1", "lv": 5, "dam": 2.35, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.5, "barrageNum": 6, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 800, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_lxb", "skillClass": "Skill_BounceThrowingKnife", "skillCompose": 8, "show": 1, "param": 2}, "1265": {"id": 1265, "class": 1, "type": 1, "name": "螺旋镖", "desc": "伤害+100%，弹射次数+3", "lv": 6, "dam": 3.35, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.5, "barrageNum": 6, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 800, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_lxb", "skillClass": "Skill_BounceThrowingKnife", "skillCompose": 5, "show": 1, "isAdGet": 1, "param": 5}, "1280": {"id": 1280, "nextId": 1281, "class": 1, "type": 1, "name": "旋风斩", "desc": "以自身为中心发动旋风斩", "lv": 1, "dam": 0.8, "area": 10, "dis": 0, "dur": 4, "damCd": 0.3, "cd": 1.5, "barrageNum": 2, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 400, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "skillClass": "Skill_Whirlwind", "skillCompose": 8, "show": 1}, "1281": {"id": 1281, "nextId": 1282, "class": 1, "type": 1, "name": "旋风斩", "desc": "伤害+10%", "lv": 2, "dam": 0.9, "area": 10, "dis": 0, "dur": 4, "damCd": 0.3, "cd": 1.5, "barrageNum": 2, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 400, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "skillClass": "Skill_Whirlwind", "skillCompose": 8, "show": 1}, "1282": {"id": 1282, "nextId": 1283, "class": 1, "type": 1, "name": "旋风斩", "desc": "范围+100%", "lv": 3, "dam": 0.9, "area": 10, "dis": 0, "dur": 4, "damCd": 0.3, "cd": 1.5, "barrageNum": 2, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 400, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "skillClass": "Skill_Whirlwind", "skillCompose": 5, "show": 1, "isAdGet": 1}, "1283": {"id": 1283, "nextId": 1284, "class": 1, "type": 1, "name": "旋风斩", "desc": "伤害+10%，持续时间+0.3s", "lv": 4, "dam": 1, "area": 10, "dis": 0, "dur": 4.3, "damCd": 0.3, "cd": 1.5, "barrageNum": 2, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 400, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "skillClass": "Skill_Whirlwind", "skillCompose": 8, "show": 1}, "1284": {"id": 1284, "nextId": 1285, "class": 1, "type": 1, "name": "旋风斩", "desc": "伤害+30%", "lv": 5, "dam": 1.3, "area": 10, "dis": 0, "dur": 4.3, "damCd": 0.3, "cd": 1.5, "barrageNum": 2, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 400, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "skillClass": "Skill_Whirlwind", "skillCompose": 8, "show": 1}, "1285": {"id": 1285, "class": 1, "type": 1, "name": "旋风斩", "desc": "增加2个扇叶，持续时间+1.7s", "lv": 6, "dam": 1.3, "area": 10, "dis": 0, "dur": 6, "damCd": 0.3, "cd": 1.5, "barrageNum": 4, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 400, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "skillClass": "Skill_Whirlwind", "skillCompose": 5, "show": 1, "isAdGet": 1}, "1300": {"id": 1300, "nextId": 1301, "class": 1, "type": 1, "name": "飓风", "desc": "生成1个飓风持续牵引怪物", "lv": 1, "dam": 0.4, "area": -1, "dis": 0, "dur": 2, "damCd": 0.3, "cd": 1.5, "barrageNum": 1, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 350, "releaseCount": 0, "releaseCd": 0, "scale": 0.7, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_jf", "skillClass": "Skill_Tornado", "skillCompose": 8, "show": 1}, "1301": {"id": 1301, "nextId": 1302, "class": 1, "type": 1, "name": "飓风", "desc": "伤害+20%", "lv": 2, "dam": 0.5, "area": -1, "dis": 0, "dur": 2, "damCd": 0.3, "cd": 1.5, "barrageNum": 1, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 350, "releaseCount": 0, "releaseCd": 0, "scale": 0.7, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_jf", "skillClass": "Skill_Tornado", "skillCompose": 8, "show": 1}, "1302": {"id": 1302, "nextId": 1303, "class": 1, "type": 1, "name": "飓风", "desc": "伤害+80%，数量+2", "lv": 3, "dam": 1.3, "area": -1, "dis": 0, "dur": 2, "damCd": 0.3, "cd": 1.5, "barrageNum": 3, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 350, "releaseCount": 0, "releaseCd": 0, "scale": 0.7, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_jf", "skillClass": "Skill_Tornado", "skillCompose": 5, "show": 1, "isAdGet": 1}, "1303": {"id": 1303, "nextId": 1304, "class": 1, "type": 1, "name": "飓风", "desc": "伤害+10%，数量+1", "lv": 4, "dam": 1.4, "area": -1, "dis": 0, "dur": 2, "damCd": 0.3, "cd": 1.5, "barrageNum": 4, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 350, "releaseCount": 0, "releaseCd": 0, "scale": 0.7, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_jf", "skillClass": "Skill_Tornado", "skillCompose": 8, "show": 1}, "1304": {"id": 1304, "nextId": 1305, "class": 1, "type": 1, "name": "飓风", "desc": "伤害+20%，数量+1", "lv": 5, "dam": 1.6, "area": -1, "dis": 0, "dur": 2, "damCd": 0.3, "cd": 1.5, "barrageNum": 5, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 350, "releaseCount": 0, "releaseCd": 0, "scale": 0.7, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_jf", "skillClass": "Skill_Tornado", "skillCompose": 8, "show": 1}, "1305": {"id": 1305, "class": 1, "type": 1, "name": "飓风", "desc": "持续时间+3s，伤害间隔-10%", "lv": 6, "dam": 1.6, "area": -1, "dis": 0, "dur": 4, "damCd": 0.2, "cd": 1.5, "barrageNum": 5, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 350, "releaseCount": 0, "releaseCd": 0, "scale": 0.7, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_jf", "skillClass": "Skill_Tornado", "skillCompose": 5, "show": 1, "isAdGet": 1}, "1320": {"id": 1320, "nextId": 1321, "class": 1, "type": 1, "name": "棒击四方", "desc": "吃俺大圣N棒", "lv": 1, "dam": 1.2, "area": -1, "dis": 0, "dur": 3.5, "damCd": 0, "cd": 1.5, "barrageNum": 1, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 400, "releaseCount": 0, "releaseCd": 0, "scale": 1.5, "isPen": 999, "isRepel": 1, "repelDic": 50, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_bjsf", "skillClass": "Skill_GoldenCudgel", "skillCompose": 8, "show": 1, "subId": "[[1340,1,1,0.5]]"}, "1321": {"id": 1321, "nextId": 1322, "class": 1, "type": 1, "name": "棒击四方", "desc": "伤害+20%", "lv": 2, "dam": 1.4, "area": -1, "dis": 0, "dur": 3.5, "damCd": 0, "cd": 1.5, "barrageNum": 1, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 400, "releaseCount": 0, "releaseCd": 0, "scale": 1.5, "isPen": 999, "isRepel": 1, "repelDic": 50, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_bjsf", "skillClass": "Skill_GoldenCudgel", "skillCompose": 8, "show": 1, "subId": "[[1341,1,1,0.5]]"}, "1322": {"id": 1322, "nextId": 1323, "class": 1, "type": 1, "name": "棒击四方", "desc": "持续时间+3s，棒刃数量+4", "lv": 3, "dam": 1.4, "area": -1, "dis": 0, "dur": 6.5, "damCd": 0, "cd": 1.5, "barrageNum": 1, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 400, "releaseCount": 0, "releaseCd": 0, "scale": 1.5, "isPen": 999, "isRepel": 1, "repelDic": 50, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_bjsf", "skillClass": "Skill_GoldenCudgel", "skillCompose": 5, "show": 1, "isAdGet": 1, "subId": "[[1342,1,1,0.5]]"}, "1323": {"id": 1323, "nextId": 1324, "class": 1, "type": 1, "name": "棒击四方", "desc": "伤害+20%", "lv": 4, "dam": 1.6, "area": -1, "dis": 0, "dur": 6.5, "damCd": 0, "cd": 1.5, "barrageNum": 1, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 400, "releaseCount": 0, "releaseCd": 0, "scale": 1.5, "isPen": 999, "isRepel": 1, "repelDic": 50, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_bjsf", "skillClass": "Skill_GoldenCudgel", "skillCompose": 8, "show": 1, "subId": "[[1343,1,1,0.5]]"}, "1324": {"id": 1324, "nextId": 1325, "class": 1, "type": 1, "name": "棒击四方", "desc": "伤害+20%", "lv": 5, "dam": 1.8, "area": -1, "dis": 0, "dur": 6.5, "damCd": 0, "cd": 1.5, "barrageNum": 1, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 400, "releaseCount": 0, "releaseCd": 0, "scale": 1.5, "isPen": 999, "isRepel": 1, "repelDic": 50, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_bjsf", "skillClass": "Skill_GoldenCudgel", "skillCompose": 8, "show": 1, "subId": "[[1344,1,1,0.5]]"}, "1325": {"id": 1325, "class": 1, "type": 1, "name": "棒击四方", "desc": "伤害+100%，棒刃体积+60%", "lv": 6, "dam": 2.7, "area": -1, "dis": 0, "dur": 6.5, "damCd": 0, "cd": 1.5, "barrageNum": 1, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 400, "releaseCount": 0, "releaseCd": 0, "scale": 1.5, "isPen": 999, "isRepel": 1, "repelDic": 50, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_bjsf", "skillClass": "Skill_GoldenCudgel", "skillCompose": 5, "show": 1, "isAdGet": 1, "subId": "[[1345,1,1,0.5]]"}, "1340": {"id": 1340, "nextId": 1341, "class": 1, "type": 1, "name": "棒刃", "desc": "棒刃", "lv": 1, "dam": 1.2, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.5, "barrageNum": 12, "barrageCd": 0.25, "barrageAngle": 60, "barrangeSpeed": 550, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "skillClass": "Skill_SwordSword", "skillCompose": 8, "show": 1}, "1341": {"id": 1341, "nextId": 1342, "class": 1, "type": 1, "name": "棒刃", "desc": "伤害+20%", "lv": 2, "dam": 1.4, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.5, "barrageNum": 12, "barrageCd": 0.25, "barrageAngle": 60, "barrangeSpeed": 550, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "skillClass": "Skill_SwordSword", "skillCompose": 8, "show": 1}, "1342": {"id": 1342, "nextId": 1343, "class": 1, "type": 1, "name": "棒刃", "desc": "持续时间+3s，棒刃数量+4", "lv": 3, "dam": 1.4, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.5, "barrageNum": 24, "barrageCd": 0.25, "barrageAngle": 60, "barrangeSpeed": 550, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "skillClass": "Skill_SwordSword", "skillCompose": 5, "show": 1}, "1343": {"id": 1343, "nextId": 1344, "class": 1, "type": 1, "name": "棒刃", "desc": "伤害+20%", "lv": 4, "dam": 1.6, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.5, "barrageNum": 24, "barrageCd": 0.25, "barrageAngle": 60, "barrangeSpeed": 550, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "skillClass": "Skill_SwordSword", "skillCompose": 8, "show": 1}, "1344": {"id": 1344, "nextId": 1345, "class": 1, "type": 1, "name": "棒刃", "desc": "伤害+20%", "lv": 5, "dam": 1.8, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.5, "barrageNum": 24, "barrageCd": 0.25, "barrageAngle": 60, "barrangeSpeed": 550, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "skillClass": "Skill_SwordSword", "skillCompose": 8, "show": 1}, "1345": {"id": 1345, "class": 1, "type": 1, "name": "棒刃", "desc": "伤害+100%，棒刃体积+60%", "lv": 6, "dam": 2.7, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.5, "barrageNum": 24, "barrageCd": 0.25, "barrageAngle": 60, "barrangeSpeed": 550, "releaseCount": 0, "releaseCd": 0, "scale": 1.6, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "skillClass": "Skill_SwordSword", "skillCompose": 5, "show": 1}, "1360": {"id": 1360, "nextId": 1361, "class": 1, "type": 1, "name": "火球", "desc": "发射火球", "lv": 1, "dam": 1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.8, "barrageNum": 1, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 700, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_fireball", "skillClass": "Skill_MagicBall", "skillCompose": 8, "show": 1, "subId": "[[1380,3,3,0]]"}, "1361": {"id": 1361, "nextId": 1362, "class": 1, "type": 1, "name": "火球", "desc": "伤害+10%", "lv": 2, "dam": 1.1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.8, "barrageNum": 1, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 700, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_fireball", "skillClass": "Skill_MagicBall", "skillCompose": 8, "show": 1, "subId": "[[1381,3,3,0]]"}, "1362": {"id": 1362, "nextId": 1363, "class": 1, "type": 1, "name": "火球", "desc": "伤害+60%，数量+2", "lv": 3, "dam": 1.7, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.8, "barrageNum": 3, "barrageCd": 0, "barrageAngle": 10, "barrangeSpeed": 700, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_fireball", "skillClass": "Skill_MagicBall", "skillCompose": 5, "show": 1, "isAdGet": 1, "subId": "[[1382,3,3,0]]"}, "1363": {"id": 1363, "nextId": 1364, "class": 1, "type": 1, "name": "火球", "desc": "伤害+20%", "lv": 4, "dam": 1.9, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.8, "barrageNum": 3, "barrageCd": 0, "barrageAngle": 10, "barrangeSpeed": 700, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_fireball", "skillClass": "Skill_MagicBall", "skillCompose": 8, "show": 1, "subId": "[[1383,3,3,0]]"}, "1364": {"id": 1364, "nextId": 1365, "class": 1, "type": 1, "name": "火球", "desc": "伤害+30%", "lv": 5, "dam": 2.1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.8, "barrageNum": 3, "barrageCd": 0, "barrageAngle": 10, "barrangeSpeed": 700, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_fireball", "skillClass": "Skill_MagicBall", "skillCompose": 8, "show": 1, "subId": "[[1384,3,3,0]]"}, "1365": {"id": 1365, "class": 1, "type": 1, "name": "火球", "desc": "伤害+100%，爆炸范围+50%", "lv": 6, "dam": 3.1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.8, "barrageNum": 3, "barrageCd": 0, "barrageAngle": 10, "barrangeSpeed": 700, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_fireball", "skillClass": "Skill_MagicBall", "skillCompose": 5, "show": 1, "isAdGet": 1, "subId": "[[1385,3,3,0]]"}, "1380": {"id": 1380, "nextId": 1381, "class": 1, "type": 1, "name": "火球范围伤害", "desc": "发射火球", "lv": 1, "dam": 1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.8, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 700, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_fireball", "skillClass": "Skill_RangeBomb", "skillCompose": 8, "show": 1}, "1381": {"id": 1381, "nextId": 1382, "class": 1, "type": 1, "name": "火球范围伤害", "desc": "伤害+10%", "lv": 2, "dam": 1.1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.8, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 700, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_fireball", "skillClass": "Skill_RangeBomb", "skillCompose": 8, "show": 1}, "1382": {"id": 1382, "nextId": 1383, "class": 1, "type": 1, "name": "火球范围伤害", "desc": "伤害+60%，数量+2", "lv": 3, "dam": 1.7, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.8, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 700, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_fireball", "skillClass": "Skill_RangeBomb", "skillCompose": 5, "show": 1}, "1383": {"id": 1383, "nextId": 1384, "class": 1, "type": 1, "name": "火球范围伤害", "desc": "伤害+20%", "lv": 4, "dam": 1.9, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.8, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 700, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_fireball", "skillClass": "Skill_RangeBomb", "skillCompose": 8, "show": 1}, "1384": {"id": 1384, "nextId": 1385, "class": 1, "type": 1, "name": "火球范围伤害", "desc": "伤害+20%", "lv": 5, "dam": 2.1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.8, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 700, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_fireball", "skillClass": "Skill_RangeBomb", "skillCompose": 8, "show": 1}, "1385": {"id": 1385, "class": 1, "type": 1, "name": "火球范围伤害", "desc": "伤害+100%，爆炸范围+50%", "lv": 6, "dam": 3.1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.8, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 700, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_fireball", "skillClass": "Skill_RangeBomb", "skillCompose": 5, "show": 1}, "1400": {"id": 1400, "nextId": 1401, "class": 1, "type": 1, "name": "方天画戟", "desc": "方天画戟从天而降", "lv": 1, "dam": 1, "area": 300, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.8, "barrageNum": 4, "barrageCd": 0.2, "barrageAngle": 0, "barrangeSpeed": 700, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 2, "icon": "img/skill/icon/skill_fthj", "skillClass": "Skill_EffectSkill", "skillCompose": 8, "show": 1, "subId": "[[1420,3,3,0]]"}, "1401": {"id": 1401, "nextId": 1402, "class": 1, "type": 1, "name": "方天画戟", "desc": "伤害+10%，数量+1", "lv": 2, "dam": 1.1, "area": 300, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.8, "barrageNum": 5, "barrageCd": 0.2, "barrageAngle": 0, "barrangeSpeed": 700, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 2, "icon": "img/skill/icon/skill_fthj", "skillClass": "Skill_EffectSkill", "skillCompose": 8, "show": 1, "subId": "[[1421,3,3,0]]"}, "1402": {"id": 1402, "nextId": 1403, "class": 1, "type": 1, "name": "方天画戟", "desc": "伤害+60%，数量+3", "lv": 3, "dam": 1.7, "area": 300, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.8, "barrageNum": 8, "barrageCd": 0.2, "barrageAngle": 0, "barrangeSpeed": 700, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 2, "icon": "img/skill/icon/skill_fthj", "skillClass": "Skill_EffectSkill", "skillCompose": 5, "show": 1, "isAdGet": 1, "subId": "[[1422,3,3,0]]"}, "1403": {"id": 1403, "nextId": 1404, "class": 1, "type": 1, "name": "方天画戟", "desc": "伤害+20%，数量+1", "lv": 4, "dam": 1.9, "area": 300, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.8, "barrageNum": 9, "barrageCd": 0.2, "barrageAngle": 0, "barrangeSpeed": 700, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 2, "icon": "img/skill/icon/skill_fthj", "skillClass": "Skill_EffectSkill", "skillCompose": 8, "show": 1, "subId": "[[1423,3,3,0]]"}, "1404": {"id": 1404, "nextId": 1405, "class": 1, "type": 1, "name": "方天画戟", "desc": "伤害+20%，数量+1", "lv": 5, "dam": 2.1, "area": 300, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.8, "barrageNum": 10, "barrageCd": 0.2, "barrageAngle": 0, "barrangeSpeed": 700, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 2, "icon": "img/skill/icon/skill_fthj", "skillClass": "Skill_EffectSkill", "skillCompose": 8, "show": 1, "subId": "[[1424,3,3,0]]"}, "1405": {"id": 1405, "class": 1, "type": 1, "name": "方天画戟", "desc": "伤害+100%，持续时间+3s", "lv": 6, "dam": 3.1, "area": 300, "dis": 0, "dur": 0, "damCd": 0, "cd": 1.8, "barrageNum": 10, "barrageCd": 0.2, "barrageAngle": 0, "barrangeSpeed": 700, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 2, "icon": "img/skill/icon/skill_fthj", "skillClass": "Skill_EffectSkill", "skillCompose": 5, "show": 1, "isAdGet": 1, "subId": "[[1425,3,3,0]]"}, "1420": {"id": 1420, "nextId": 1421, "class": 1, "type": 1, "name": "方天画戟（范围伤害）", "desc": "方天画戟停留一会才消失", "lv": 1, "dam": 1, "area": 300, "dis": 0, "dur": 3, "damCd": 0.5, "cd": 1.8, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_fthj", "skillClass": "Skill_EffectSkill", "skillCompose": 8, "show": 1}, "1421": {"id": 1421, "nextId": 1422, "class": 1, "type": 1, "name": "方天画戟（范围伤害）", "desc": "伤害+10%，数量+1", "lv": 2, "dam": 1.1, "area": 300, "dis": 0, "dur": 3, "damCd": 0.5, "cd": 1.8, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_fthj", "skillClass": "Skill_EffectSkill", "skillCompose": 8, "show": 1}, "1422": {"id": 1422, "nextId": 1423, "class": 1, "type": 1, "name": "方天画戟（范围伤害）", "desc": "伤害+60%，数量+3", "lv": 3, "dam": 1.7, "area": 300, "dis": 0, "dur": 3, "damCd": 0.5, "cd": 1.8, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_fthj", "skillClass": "Skill_EffectSkill", "skillCompose": 5, "show": 1}, "1423": {"id": 1423, "nextId": 1424, "class": 1, "type": 1, "name": "方天画戟（范围伤害）", "desc": "伤害+20%，数量+1", "lv": 4, "dam": 1.9, "area": 300, "dis": 0, "dur": 3, "damCd": 0.5, "cd": 1.8, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_fthj", "skillClass": "Skill_EffectSkill", "skillCompose": 8, "show": 1}, "1424": {"id": 1424, "nextId": 1425, "class": 1, "type": 1, "name": "方天画戟（范围伤害）", "desc": "伤害+20%，数量+1", "lv": 5, "dam": 2.1, "area": 300, "dis": 0, "dur": 3, "damCd": 0.5, "cd": 1.8, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_fthj", "skillClass": "Skill_EffectSkill", "skillCompose": 8, "show": 1}, "1425": {"id": 1425, "class": 1, "type": 1, "name": "方天画戟（范围伤害）", "desc": "伤害+100%，持续时间+3s", "lv": 6, "dam": 3.1, "area": 300, "dis": 0, "dur": 6, "damCd": 0.5, "cd": 1.8, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_fthj", "skillClass": "Skill_EffectSkill", "skillCompose": 5, "show": 1}, "1440": {"id": 1440, "nextId": 1441, "class": 1, "type": 1, "name": "蓄力斩", "desc": "蓄力1段", "lv": 1, "dam": 1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 1, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 500, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 2, "icon": "img/skill/icon/skill_fthj", "skillClass": "Skill_EffectSkill", "skillCompose": 8, "show": 1, "subId": "[[1460,1,1,0]]"}, "1441": {"id": 1441, "nextId": 1442, "class": 1, "type": 1, "name": "蓄力斩", "desc": "伤害+10%", "lv": 2, "dam": 1.1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 1, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 500, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 2, "icon": "img/skill/icon/skill_fthj", "skillClass": "Skill_EffectSkill", "skillCompose": 8, "show": 1, "subId": "[[1461,1,1,0]]"}, "1442": {"id": 1442, "nextId": 1443, "class": 1, "type": 1, "name": "蓄力斩", "desc": "蓄力2段", "lv": 3, "dam": 1.7, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 3, "barrageNum": 1, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 500, "releaseCount": 0, "releaseCd": 0, "scale": 1.5, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 2, "icon": "img/skill/icon/skill_fthj", "skillClass": "Skill_EffectSkill", "skillCompose": 5, "show": 1, "isAdGet": 1, "subId": "[[1462,1,1,0]]"}, "1443": {"id": 1443, "nextId": 1444, "class": 1, "type": 1, "name": "蓄力斩", "desc": "伤害+20%", "lv": 4, "dam": 1.9, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 3, "barrageNum": 1, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 500, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 2, "icon": "img/skill/icon/skill_fthj", "skillClass": "Skill_EffectSkill", "skillCompose": 8, "show": 1, "subId": "[[1463,1,1,0]]"}, "1444": {"id": 1444, "nextId": 1445, "class": 1, "type": 1, "name": "蓄力斩", "desc": "伤害+30%", "lv": 5, "dam": 2.1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 3, "barrageNum": 1, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 500, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 2, "icon": "img/skill/icon/skill_fthj", "skillClass": "Skill_EffectSkill", "skillCompose": 8, "show": 1, "subId": "[[1464,1,1,0]]"}, "1445": {"id": 1445, "class": 1, "type": 1, "name": "蓄力斩", "desc": "蓄力3段", "lv": 6, "dam": 3.1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 4, "barrageNum": 1, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 500, "releaseCount": 0, "releaseCd": 0, "scale": 3, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 2, "icon": "img/skill/icon/skill_fthj", "skillClass": "Skill_EffectSkill", "skillCompose": 5, "show": 1, "isAdGet": 1, "subId": "[[1465,1,1,0]]"}, "1460": {"id": 1460, "nextId": 1461, "class": 1, "type": 1, "name": "蓄力斩（范围防御）", "desc": "方天画戟停留一会才消失", "lv": 1, "dam": 1, "area": 300, "dis": 0, "dur": 2, "damCd": 0.5, "cd": 0.3, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_fthj", "skillClass": "Skill_EffectSkill", "skillCompose": 8, "show": 1}, "1461": {"id": 1461, "nextId": 1462, "class": 1, "type": 1, "name": "蓄力斩（范围防御）", "desc": "伤害+10%，数量+1", "lv": 2, "dam": 1.1, "area": 300, "dis": 0, "dur": 2, "damCd": 0.5, "cd": 0.3, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_fthj", "skillClass": "Skill_EffectSkill", "skillCompose": 8, "show": 1}, "1462": {"id": 1462, "nextId": 1463, "class": 1, "type": 1, "name": "蓄力斩（范围防御）", "desc": "伤害+60%，数量+3", "lv": 3, "dam": 1.7, "area": 300, "dis": 0, "dur": 3, "damCd": 0.5, "cd": 0.3, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_fthj", "skillClass": "Skill_EffectSkill", "skillCompose": 5, "show": 1}, "1463": {"id": 1463, "nextId": 1464, "class": 1, "type": 1, "name": "蓄力斩（范围防御）", "desc": "伤害+20%，数量+1", "lv": 4, "dam": 1.9, "area": 300, "dis": 0, "dur": 3, "damCd": 0.5, "cd": 0.3, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_fthj", "skillClass": "Skill_EffectSkill", "skillCompose": 8, "show": 1}, "1464": {"id": 1464, "nextId": 1465, "class": 1, "type": 1, "name": "蓄力斩（范围防御）", "desc": "伤害+20%，数量+1", "lv": 5, "dam": 2.1, "area": 300, "dis": 0, "dur": 3, "damCd": 0.5, "cd": 0.3, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_fthj", "skillClass": "Skill_EffectSkill", "skillCompose": 8, "show": 1}, "1465": {"id": 1465, "class": 1, "type": 1, "name": "蓄力斩（范围防御）", "desc": "伤害+100%，持续时间+3s", "lv": 6, "dam": 3.1, "area": 300, "dis": 0, "dur": 4, "damCd": 0.5, "cd": 0.3, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "icon": "img/skill/icon/skill_fthj", "skillClass": "Skill_EffectSkill", "skillCompose": 5, "show": 1}, "1480": {"id": 1480, "nextId": 1481, "class": 1, "type": 1, "name": "雷电", "desc": "掌控雷电", "lv": 1, "dam": 1, "area": -1, "dis": 0, "dur": 3, "damCd": 0.5, "cd": 2, "barrageNum": 2, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 300, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 1, "icon": "img/skill/icon/skill_mfq", "skillClass": "Skill_MagicBall", "skillCompose": 8, "show": 1, "subId": "[[1500,2,2,0],[1520,4,3,0]]"}, "1481": {"id": 1481, "nextId": 1482, "class": 1, "type": 1, "name": "雷电", "desc": "伤害+10%", "lv": 2, "dam": 1.1, "area": -1, "dis": 0, "dur": 3, "damCd": 0.5, "cd": 2, "barrageNum": 2, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 300, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 1, "icon": "img/skill/icon/skill_mfq", "skillClass": "Skill_MagicBall", "skillCompose": 8, "show": 1, "subId": "[[1501,2,2,0],[1521,4,3,0]]"}, "1482": {"id": 1482, "nextId": 1483, "class": 1, "type": 1, "name": "雷电", "desc": "伤害+60%，数量+2", "lv": 3, "dam": 1.6, "area": -1, "dis": 0, "dur": 3, "damCd": 0.5, "cd": 2, "barrageNum": 4, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 300, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 1, "icon": "img/skill/icon/skill_mfq", "skillClass": "Skill_MagicBall", "skillCompose": 5, "show": 1, "isAdGet": 1, "subId": "[[1502,2,2,0],[1522,4,3,0]]"}, "1483": {"id": 1483, "nextId": 1484, "class": 1, "type": 1, "name": "雷电", "desc": "伤害+20%", "lv": 4, "dam": 1.7, "area": -1, "dis": 0, "dur": 3, "damCd": 0.5, "cd": 2, "barrageNum": 4, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 300, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 1, "icon": "img/skill/icon/skill_mfq", "skillClass": "Skill_MagicBall", "skillCompose": 8, "show": 1, "subId": "[[1503,2,2,0],[1523,4,3,0]]"}, "1484": {"id": 1484, "nextId": 1485, "class": 1, "type": 1, "name": "雷电", "desc": "伤害+20%", "lv": 5, "dam": 1.8, "area": -1, "dis": 0, "dur": 3, "damCd": 0.5, "cd": 2, "barrageNum": 4, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 300, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 1, "icon": "img/skill/icon/skill_mfq", "skillClass": "Skill_MagicBall", "skillCompose": 8, "show": 1, "subId": "[[1504,2,2,0],[1524,4,3,0]]"}, "1485": {"id": 1485, "class": 1, "type": 1, "name": "雷电", "desc": "数量+2,持续时间+3s", "lv": 6, "dam": 2.8, "area": -1, "dis": 0, "dur": 6, "damCd": 0.5, "cd": 2, "barrageNum": 6, "barrageCd": 0.1, "barrageAngle": 0, "barrangeSpeed": 300, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 1, "icon": "img/skill/icon/skill_mfq", "skillClass": "Skill_MagicBall", "skillCompose": 5, "show": 1, "isAdGet": 1, "subId": "[[1505,2,2,0],[1525,4,3,0]]"}, "1500": {"id": 1500, "nextId": 1501, "class": 1, "type": 1, "name": "雷电（叉状闪电）", "desc": "雷电（叉状闪电）", "lv": 1, "dam": 1.1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 1, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 6, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_sdl", "skillClass": "Skill_<PERSON><PERSON><PERSON><PERSON>", "skillCompose": 8, "show": 1}, "1501": {"id": 1501, "nextId": 1502, "class": 1, "type": 1, "name": "雷电（叉状闪电）", "desc": "伤害+10%", "lv": 2, "dam": 1.3, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 1, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 6, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_sdl", "skillClass": "Skill_<PERSON><PERSON><PERSON><PERSON>", "skillCompose": 8, "show": 1}, "1502": {"id": 1502, "nextId": 1503, "class": 1, "type": 1, "name": "雷电（叉状闪电）", "desc": "伤害+60%，数量+2", "lv": 3, "dam": 1.7, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 1, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 6, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_sdl", "skillClass": "Skill_<PERSON><PERSON><PERSON><PERSON>", "skillCompose": 5, "show": 1}, "1503": {"id": 1503, "nextId": 1504, "class": 1, "type": 1, "name": "雷电（叉状闪电）", "desc": "伤害+20%", "lv": 4, "dam": 1.9, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 1, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 6, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_sdl", "skillClass": "Skill_<PERSON><PERSON><PERSON><PERSON>", "skillCompose": 8, "show": 1}, "1504": {"id": 1504, "nextId": 1505, "class": 1, "type": 1, "name": "雷电（叉状闪电）", "desc": "伤害+20%", "lv": 5, "dam": 2.1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 1, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 6, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_sdl", "skillClass": "Skill_<PERSON><PERSON><PERSON><PERSON>", "skillCompose": 8, "show": 1}, "1505": {"id": 1505, "class": 1, "type": 1, "name": "雷电（叉状闪电）", "desc": "数量+2,持续时间+3s", "lv": 6, "dam": 3.1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 1, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 6, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_sdl", "skillClass": "Skill_<PERSON><PERSON><PERSON><PERSON>", "skillCompose": 5, "show": 1}, "1520": {"id": 1520, "nextId": 1521, "class": 1, "type": 1, "name": "雷电（结束爆炸）", "desc": "雷电（结束爆炸）", "lv": 1, "dam": 1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_fireball", "skillClass": "Skill_RangeBomb", "skillCompose": 8, "show": 1}, "1521": {"id": 1521, "nextId": 1522, "class": 1, "type": 1, "name": "雷电（结束爆炸）", "desc": "伤害+10%", "lv": 2, "dam": 1.1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_fireball", "skillClass": "Skill_RangeBomb", "skillCompose": 8, "show": 1}, "1522": {"id": 1522, "nextId": 1523, "class": 1, "type": 1, "name": "雷电（结束爆炸）", "desc": "伤害+60%，数量+2", "lv": 3, "dam": 1.7, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_fireball", "skillClass": "Skill_RangeBomb", "skillCompose": 5, "show": 1}, "1523": {"id": 1523, "nextId": 1524, "class": 1, "type": 1, "name": "雷电（结束爆炸）", "desc": "伤害+20%", "lv": 4, "dam": 1.9, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_fireball", "skillClass": "Skill_RangeBomb", "skillCompose": 8, "show": 1}, "1524": {"id": 1524, "nextId": 1525, "class": 1, "type": 1, "name": "雷电（结束爆炸）", "desc": "伤害+20%", "lv": 5, "dam": 2.1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_fireball", "skillClass": "Skill_RangeBomb", "skillCompose": 8, "show": 1}, "1525": {"id": 1525, "class": 1, "type": 1, "name": "雷电（结束爆炸）", "desc": "数量+2,持续时间+3s", "lv": 6, "dam": 3.1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 2, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "icon": "img/skill/icon/skill_fireball", "skillClass": "Skill_RangeBomb", "skillCompose": 5, "show": 1}, "10000": {"id": 10000, "class": 1, "type": 1, "name": "冲撞", "desc": "第1关boss冲撞技能", "lv": 1, "dam": 2, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 4, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 300, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 1, "tartype": 1, "skillClass": "Skill_BrutalImpact", "skillCompose": 8, "show": 1, "param": "1,2,3", "subId": "[[10020,9,0,0]]", "animation": "skill01"}, "10020": {"id": 10020, "class": 1, "type": 1, "name": "砸地板石头", "desc": "第1关boss砸地板产生石头", "lv": 1, "dam": 1, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 4, "barrageNum": 12, "barrageCd": 0, "barrageAngle": 30, "barrangeSpeed": 500, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 999, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "skillClass": "Skill_<PERSON>l", "skillCompose": 8, "show": 1, "param": "1,2,3,4", "animation": "skill03"}, "10040": {"id": 10040, "class": 1, "type": 1, "name": "召唤小怪", "desc": "第1关boss召唤小怪", "lv": 1, "dam": 0, "area": -1, "dis": 0, "dur": 0, "damCd": 0, "cd": 10, "barrageNum": 0, "barrageCd": 0, "barrageAngle": 0, "barrangeSpeed": 0, "releaseCount": 0, "releaseCd": 0, "scale": 1, "isPen": 1, "isRepel": 0, "repelDic": 0, "isLock": 0, "tartype": 0, "skillClass": "Skill_<PERSON><PERSON>on", "skillCompose": 8, "show": 1, "param": "1000,1001,1002", "animation": "skill02"}}