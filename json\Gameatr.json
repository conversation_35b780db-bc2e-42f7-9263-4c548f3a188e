{"1": {"id": 1, "define": "hp", "name": "全局生命", "icon": "v1/images/icon/icon_heart", "unit": "%"}, "2": {"id": 2, "define": "recover", "name": "恢复生命"}, "3": {"id": 3, "define": "roleatk", "name": "全局伤害", "icon": "v1/images/icon/icon_attack", "unit": "%"}, "4": {"id": 4, "define": "cirtdam", "name": "暴击伤害"}, "5": {"id": 5, "define": "cirtrate", "name": "暴击率"}, "6": {"id": 6, "define": "hitrate", "name": "命中率"}, "7": {"id": 7, "define": "dodge", "name": "闪避率"}, "8": {"id": 8, "define": "movespeed", "name": "移速"}, "9": {"id": 9, "define": "exp", "name": "经验值"}, "10": {"id": 10, "define": "itemarea", "name": "拾取范围"}, "11": {"id": 11, "define": "roledef", "name": "角色防御"}, "12": {"id": 12, "define": "invincible", "name": "角色无敌"}, "13": {"id": 13, "define": "modeScale", "name": "模型体积"}, "14": {"id": 14, "define": "monsterOffset", "name": "怪物刷新间隔"}, "15": {"id": 15, "define": "wokermovespeed", "name": "搬运工移速"}, "16": {"id": 16, "define": "spirit", "name": "治疗"}, "100": {"id": 100, "define": "skilldam", "name": "技能伤害"}, "101": {"id": 101, "define": "barrageNum", "name": "技能弹道数量"}, "102": {"id": 102, "define": "scale", "name": "技能体积"}, "103": {"id": 103, "define": "cd", "name": "技能cd"}, "104": {"id": 104, "define": "dur", "name": "技能持续时间"}, "105": {"id": 105, "define": "damcd", "name": "技能伤害间隔"}, "106": {"id": 106, "define": "freeze", "name": "停止行动"}, "107": {"id": 107, "define": "rotate", "name": "旋转速度"}, "108": {"id": 108, "define": "combo", "name": "技能释放次数"}, "109": {"id": 109, "define": "buffEffect", "name": "技能增加Buff", "isOverLay": 0}, "110": {"id": 110, "define": "attackarea", "name": "攻击范围"}, "111": {"id": 111, "define": "slayingDamage_30", "name": "低血增伤"}, "112": {"id": 112, "define": "camera", "name": "镜头远近"}, "113": {"id": 113, "define": "damadd", "name": "条件增伤"}, "114": {"id": 114, "define": "damdis", "name": "距离增伤"}, "115": {"id": 115, "define": "beheaded", "name": "低血斩杀", "isOverLay": 0}, "116": {"id": 116, "define": "dropWood", "name": "掉落木材数量附加"}, "117": {"id": 117, "define": "getCoinRate", "name": "矿工采集金币附加"}, "118": {"id": 118, "define": "woodChange", "name": "掉落木材转化成金币"}, "119": {"id": 119, "define": "repel", "name": "击退"}, "120": {"id": 120, "define": "penNum", "name": "穿透数量"}, "121": {"id": 121, "define": "shield<PERSON>er", "name": "百分比回护甲增益"}, "122": {"id": 122, "define": "buffSubSkill", "name": "关联子技能", "isOverLay": 0}, "123": {"id": 123, "define": "roundReward", "name": "回合奖励"}, "124": {"id": 124, "define": "buffDam", "name": "buff伤害"}, "125": {"id": 125, "define": "hpPer", "name": "百分比回血增益"}, "126": {"id": 126, "define": "shieldBlock", "name": "盾牌格挡伤害"}, "127": {"id": 127, "define": "deepHurt", "name": "重伤"}, "128": {"id": 128, "define": "bulletTime", "name": "弹道时间"}, "129": {"id": 129, "define": "buff<PERSON><PERSON><PERSON>", "name": "buff层数"}, "130": {"id": 130, "define": "ReboundDamPer", "name": "百分比反弹伤害"}, "131": {"id": 131, "define": "ReboundDamVal", "name": "固定值反弹伤害"}, "132": {"id": 132, "define": "addshieldper", "name": "百分比加护甲"}, "133": {"id": 133, "define": "barrageSpeed", "name": "弹道速度"}, "134": {"id": 134, "define": "repeled", "name": "被击退"}, "135": {"id": 135, "define": "replaceBullet", "name": "替换子弹id", "isOverLay": 0, "target": 2}, "136": {"id": 136, "define": "vampireEffect", "name": "吸血效果"}, "137": {"id": 137, "define": "sheildLimit", "name": "护甲上限增益"}, "138": {"id": 138, "define": "buff<PERSON><PERSON>ght", "name": "buff权重"}, "139": {"id": 139, "define": "replaceSkill", "name": "替换技能", "isOverLay": 0}, "140": {"id": 140, "define": "<PERSON><PERSON><PERSON>", "name": "替换buff", "isOverLay": 0}, "141": {"id": 141, "define": "useSkill", "name": "使用技能", "isOverLay": 0}, "142": {"id": 142, "define": "addObjectBuff", "name": "给对象加buff", "isOverLay": 0}, "143": {"id": 143, "define": "addSkill", "name": "给角色加技能", "isOverLay": 0}, "144": {"id": 144, "define": "areaDam", "name": "范围伤害"}, "145": {"id": 145, "define": "buff<PERSON>ur", "name": "buff持续时间"}, "146": {"id": 146, "define": "resistDam", "name": "抵挡伤害"}, "147": {"id": 147, "define": "buffEffectAdd", "name": "buff效果增益"}, "148": {"id": 148, "define": "skillCrit", "name": "技能暴击率"}, "149": {"id": 149, "define": "skillCritNum", "name": "技能暴击伤害"}, "150": {"id": 150, "define": "otherValueAdd", "name": "其他参数增益"}, "151": {"id": 151, "define": "replaceRole", "name": "替换角色"}, "152": {"id": 152, "define": "buff<PERSON>ei<PERSON><PERSON><PERSON>e", "name": "buff概率改变"}, "153": {"id": 153, "define": "subSkillTrigger", "name": "子技能触发次数"}, "200": {"id": 200, "define": "normal", "name": "普通属性技能"}, "201": {"id": 201, "define": "fire", "name": "火属性技能"}, "202": {"id": 202, "define": "ice", "name": "冰属性技能"}, "203": {"id": 203, "define": "electricity", "name": "电属性技能"}, "204": {"id": 204, "define": "wind", "name": "风属性技能"}, "300": {"id": 300, "define": "monsterSplit", "name": "怪物分裂"}, "301": {"id": 301, "define": "monsterAppear", "name": "怪物出现概率"}, "400": {"id": 400, "define": "addMana", "name": "增加法力值"}, "401": {"id": 401, "define": "hpval", "name": "生命"}, "402": {"id": 402, "define": "recoverval", "name": "恢复生命"}, "403": {"id": 403, "define": "<PERSON><PERSON>kval", "name": "角色攻击"}, "404": {"id": 404, "define": "c<PERSON><PERSON><PERSON>", "name": "暴击伤害"}, "406": {"id": 406, "define": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "产出法力值"}, "407": {"id": 407, "define": "skillcdval", "name": "技能cd"}, "408": {"id": 408, "define": "shieldval", "name": "回盾固定值"}, "500": {"id": 500, "define": "reflexCount", "name": "子弹反弹"}, "501": {"id": 501, "define": "lastAttackCrit", "name": "最后1击暴击"}, "502": {"id": 502, "define": "disarm", "name": "缴械"}, "1000": {"id": 1000, "define": "bagGridCd", "name": "背包相连格子减cd"}, "1001": {"id": 1001, "define": "bagGridDam", "name": "背包相连格子增伤"}, "1002": {"id": 1002, "define": "bagGrid<PERSON><PERSON>ar", "name": "背包格子出现概率"}, "1003": {"id": 1003, "define": "bagGridCritRate", "name": "背包相连格子增暴击率"}, "1004": {"id": 1004, "define": "bagGridCritDam", "name": "背包相连格子增伤"}, "2000": {"id": 2000, "define": "targetequip", "name": "目标", "icon": "v1/images/icon/icon_target"}, "2001": {"id": 2001, "define": "cdequip", "name": "冷却", "icon": "v1/images/icon/icon_clock", "unit": "s"}, "2002": {"id": 2002, "define": "atkequip", "name": "伤害", "icon": "v1/images/icon/icon_attack"}, "2003": {"id": 2003, "define": "hpequip", "name": "生命", "icon": "v1/images/icon/icon_heart"}, "2004": {"id": 2004, "define": "rangeequip", "name": "射程", "icon": "v1/images/icon/icon_range"}, "2005": {"id": 2005, "define": "healequip", "name": "回复", "icon": "v1/images/icon/icon_heart_+"}, "2006": {"id": 2006, "define": "shieldequip", "name": "护甲", "icon": "v1/images/icon/icon_shield_+"}, "2007": {"id": 2007, "define": "silvercoinequip", "name": "银币", "icon": "v1/images/icon/icon_money"}, "2008": {"id": 2008, "define": "healeratequip", "name": "回复", "icon": "v1/images/icon/icon_heart_+", "unit": "%"}, "3000": {"id": 3000, "define": "alldam", "name": "全局伤害", "icon": "v1/images/icon/icon_attack", "unit": "%"}, "3001": {"id": 3001, "define": "allhp", "name": "全局生命", "icon": "v1/images/icon/icon_heart", "unit": "%"}, "-1": {"id": -1, "name": "其他"}}