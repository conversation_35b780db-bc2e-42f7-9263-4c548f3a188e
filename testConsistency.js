const fs = require('fs');
const path = require('path');

// 比较两个JSON对象是否相等
function deepEqual(obj1, obj2) {
    if (obj1 === obj2) return true;
    
    if (obj1 == null || obj2 == null) return false;
    
    if (typeof obj1 !== typeof obj2) return false;
    
    if (typeof obj1 !== 'object') return obj1 === obj2;
    
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);
    
    if (keys1.length !== keys2.length) return false;
    
    for (let key of keys1) {
        if (!keys2.includes(key)) return false;
        if (!deepEqual(obj1[key], obj2[key])) return false;
    }
    
    return true;
}

// 测试特定文件的一致性
function testFileConsistency(fileName) {
    const originalPath = path.join('./json', fileName);
    
    // 备份原始文件
    const backupPath = path.join('./json', fileName + '.backup');
    if (fs.existsSync(originalPath)) {
        fs.copyFileSync(originalPath, backupPath);
    }
    
    try {
        // 读取备份的原始JSON
        const originalContent = fs.readFileSync(backupPath, 'utf8');
        const originalData = JSON.parse(originalContent);
        
        // 读取转换后的JSON
        const convertedContent = fs.readFileSync(originalPath, 'utf8');
        const convertedData = JSON.parse(convertedContent);
        
        // 比较两个对象
        const isEqual = deepEqual(originalData, convertedData);
        
        console.log(`\n=== Testing ${fileName} ===`);
        console.log(`Original keys: ${Object.keys(originalData).length}`);
        console.log(`Converted keys: ${Object.keys(convertedData).length}`);
        console.log(`Consistency check: ${isEqual ? 'PASS' : 'FAIL'}`);
        
        if (!isEqual) {
            console.log('\nDifferences found:');
            // 检查缺失的键
            const originalKeys = Object.keys(originalData);
            const convertedKeys = Object.keys(convertedData);
            
            const missingInConverted = originalKeys.filter(key => !convertedKeys.includes(key));
            const extraInConverted = convertedKeys.filter(key => !originalKeys.includes(key));
            
            if (missingInConverted.length > 0) {
                console.log(`Missing in converted: ${missingInConverted.join(', ')}`);
            }
            if (extraInConverted.length > 0) {
                console.log(`Extra in converted: ${extraInConverted.join(', ')}`);
            }
            
            // 检查值的差异
            for (let key of originalKeys) {
                if (convertedKeys.includes(key)) {
                    if (!deepEqual(originalData[key], convertedData[key])) {
                        console.log(`Value difference in '${key}':`);
                        console.log(`  Original:`, JSON.stringify(originalData[key], null, 2));
                        console.log(`  Converted:`, JSON.stringify(convertedData[key], null, 2));
                    }
                }
            }
        }
        
        return isEqual;
    } catch (err) {
        console.error(`Error testing ${fileName}: ${err.message}`);
        return false;
    }
}

// 主测试函数
function main() {
    console.log('Testing JSON to CSV to JSON consistency...');
    
    // 测试 test_data.json
    const testResult = testFileConsistency('test_data.json');
    
    if (testResult) {
        console.log('\n✅ Consistency test PASSED! The conversion maintains data integrity.');
    } else {
        console.log('\n❌ Consistency test FAILED! There are differences in the converted data.');
    }
}

// 执行测试
main();